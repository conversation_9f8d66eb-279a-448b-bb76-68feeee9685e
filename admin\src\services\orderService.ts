import { CustomerService } from "./customerService";
import { api, endpoints } from "../lib/api";
import { ShowroomOrderType } from "../components/orders/Create/ShowroomOrderSection";
import { OrderItem as BaseOrderItem } from "../types/order";

// === TYPES AND INTERFACES ===

export interface OrderDiscountCalculation {
  subtotal: number;
  rankDiscount: number;
  shippingFee: number;
  total: number;
  discountPercentage: number;
  totalDiscount: number;
}

export interface CreateOrderData {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  notes?: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_unit?: string;
  shipping_fee?: number;
  is_showroom: boolean;
  is_e_comm?: boolean;
  delivery_date?: string;
  items: {
    product: number;
    variant?: number;
    quantity: number;
    chiet_khau_amount?: number;
  }[];
}

// Showroom-specific types
export interface OrderItem extends BaseOrderItem {
  unit?: string;
}

export interface OrderForm {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  notes?: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_unit?: string;
  shipping_fee?: number;
  discount?: number;
  is_showroom: boolean;
  is_e_comm?: boolean;
  delivery_date?: string;
  tax?: number;
  have_tax: boolean;
}

export interface TransportInfo {
  name: string;
  address: string;
  phone: string;
  operatingHours: string;
}

export interface CreateOrderParams {
  orderData: OrderForm;
  items: OrderItem[];
  useTransportation: boolean;
  transportInfo: TransportInfo;
  shippingFee: number;
  notes: string;
}

export class OrderService {
  /**
   * Calculate order totals with rank discount
   */
  static calculateOrderTotals(
    subtotal: number,
    shippingFee: number,
    customerRank: 'normal' | 'silver' | 'gold',
    applyRankDiscount: boolean = true,
    existingDiscount: number = 0
  ): OrderDiscountCalculation {
    const discountPercentage = applyRankDiscount
      ? CustomerService.RANK_DISCOUNTS[customerRank]
      : 0;

    // Calculate rank discount on the amount after existing discount
    const discountedAmount = subtotal - existingDiscount;
    const rankDiscount = applyRankDiscount
      ? CustomerService.calculateRankDiscount(discountedAmount, customerRank)
      : 0;

    const totalDiscount = existingDiscount + rankDiscount;
    const total = subtotal - totalDiscount + shippingFee;

    return {
      subtotal,
      rankDiscount,
      shippingFee,
      total,
      discountPercentage,
      totalDiscount,
    };
  }

  /**
   * Get order discount breakdown for display
   */
  static getDiscountBreakdown(
    subtotal: number,
    shippingFee: number,
    customerRank: 'normal' | 'silver' | 'gold',
    applyRankDiscount: boolean = true,
    existingDiscount: number = 0
  ) {
    const calculation = this.calculateOrderTotals(subtotal, shippingFee, customerRank, applyRankDiscount, existingDiscount);
    const rankInfo = CustomerService.getRankInfo(customerRank);

    const items: Array<{
      label: string;
      amount: number;
      type: 'subtotal' | 'discount' | 'shipping';
    }> = [
      {
        label: 'Tạm tính',
        amount: calculation.subtotal,
        type: 'subtotal' as const,
      }
    ];

    // Add existing discount if any
    if (existingDiscount > 0) {
      items.push({
        label: 'Giảm giá khác',
        amount: -existingDiscount,
        type: 'discount' as const,
      });
    }

    // Add rank discount if applicable
    if (calculation.rankDiscount > 0) {
      items.push({
        label: `Giảm giá ${rankInfo.label} (${calculation.discountPercentage}%)`,
        amount: -calculation.rankDiscount,
        type: 'discount' as const,
      });
    }

    // Add shipping fee
    items.push({
      label: 'Phí vận chuyển',
      amount: calculation.shippingFee,
      type: 'shipping' as const,
    });

    return {
      items,
      total: calculation.total,
      savings: calculation.rankDiscount,
      totalDiscount: calculation.totalDiscount,
    };
  }

  /**
   * Create a single order with specified showroom settings
   */
  static async createSingleOrder(
    params: CreateOrderParams,
    isShowroomOrder: boolean = false
  ) {
    const { orderData, items, useTransportation, transportInfo, shippingFee, notes } = params;

    // Check if this is an e-commerce order (should have 0 price like showroom)
    const isECommerceOrder = orderData.is_e_comm || false;
    
    const orderPayload = {
      ...orderData,
      notes,
      is_showroom: isShowroomOrder,
      is_e_comm: isECommerceOrder,
      shipping_fee: shippingFee,
      items: items.map((item) => ({
        product: item.product,
        variant: item.variant,
        quantity: item.quantity,
        chiet_khau_amount: item.chiet_khau_amount || 0,
      })),
    };

    // Add shipping_unit if using transportation
    if (useTransportation) {
      orderPayload.shipping_unit = "transport_partner";

      // If transport is enabled, append transport info to the customer's address
      if (transportInfo.name && transportInfo.address) {
        const transportAddressText = `\nThông tin chành xe: ${
          transportInfo.name
        }, ${transportInfo.address}${
          transportInfo.phone ? `, SĐT: ${transportInfo.phone}` : ""
        }${
          transportInfo.operatingHours
            ? `, Giờ hoạt động: ${transportInfo.operatingHours}`
            : ""
        }`;

        // Append transport info to the shipping address
        orderPayload.shipping_address =
          orderPayload.shipping_address + transportAddressText;
      }
    }

    return await api.post(endpoints.orders.create, orderPayload);
  }

  /**
   * Create combined orders (warehouse to showroom + showroom to customer)
   */
  static async createCombinedOrders(params: CreateOrderParams) {
    // First order: Warehouse to Showroom (is_showroom = true, price = 0)
    const warehouseToShowroomOrder = await this.createSingleOrder(params, true);

    // Second order: Showroom to Customer (is_showroom = false, normal price)
    const showroomToCustomerOrder = await this.createSingleOrder(params, false);

    return {
      warehouseOrder: warehouseToShowroomOrder,
      customerOrder: showroomToCustomerOrder,
    };
  }

  /**
   * Handle order creation based on showroom order type
   */
  static async handleOrderCreation(
    showroomOrderType: ShowroomOrderType | null,
    params: CreateOrderParams
  ) {
    if (showroomOrderType === 'combined_orders') {
      // Option 3: Create 2 orders
      const result = await this.createCombinedOrders(params);
      return {
        type: 'combined' as const,
        orders: result,
        message: "Tạo 2 đơn hàng thành công",
        redirectOrderId: result.customerOrder.data.id,
      };
    } else {
      // Default, Option 1, 2, or e-commerce: Create single order
      const isShowroom = showroomOrderType === 'warehouse_to_showroom';
      const response = await this.createSingleOrder(params, isShowroom);

      const orderTypeMessage = showroomOrderType === 'e_commerce_order'
        ? "Tạo đơn hàng TMĐT thành công"
        : "Tạo đơn hàng thành công";

      return {
        type: 'single' as const,
        order: response,
        message: orderTypeMessage,
        redirectOrderId: response.data.id,
      };
    }
  }

  /**
   * Handle showroom order type selection and update items accordingly
   */
  static handleShowroomOrderTypeSelection(
    type: ShowroomOrderType,
    _items: OrderItem[], // Prefix with underscore to indicate intentionally unused
    setItems: (items: OrderItem[] | ((prev: OrderItem[]) => OrderItem[])) => void,
    _showToast: (message: string, type: 'success' | 'error' | 'info') => void // Prefix with underscore to indicate intentionally unused
  ) {
      switch (type) {
        case 'warehouse_to_showroom':
        case 'e_commerce_order':
          // Set items to 0 price
          setItems((prevItems) =>
            prevItems.map((item) => ({ ...item, total_price: 0 }))
          );
          break;
        case 'normal_order':
        case 'normal_revenue':
        case 'combined_orders':
          // Restore normal pricing
          setItems((prevItems) =>
            prevItems.map((item) => ({
              ...item,
              total_price: Math.round(item.price * item.quantity),
            }))
          );
          break;
    }
  }

  /**
   * Handle showroom toggle (for backward compatibility)
   */
  static handleShowroomToggle(
    value: boolean,
    _items: OrderItem[], // Prefix with underscore to indicate intentionally unused
    setItems: (items: OrderItem[] | ((prev: OrderItem[]) => OrderItem[])) => void,
    showToast: (message: string, type: 'success' | 'error' | 'info') => void
  ) {
    if (value) {
      setItems((prevItems) =>
        prevItems.map((item) => ({
          ...item,
          total_price: 0,
        }))
      );
    } else {
      setItems([]);
      showToast("Vui lòng chọn lại sản phẩm", "info");
    }
  }
}
