import { Typography } from "antd";
import type { ColumnsType } from "antd/es/table";
import { ChainProductItem } from "@/hooks/orders";

const { Text } = Typography;

export function createChainProductColumns(): ColumnsType<ChainProductItem> {
  return [
    {
      title: "Mã sản phẩm",
      dataIndex: "code",
      key: "code",
      width: 120,
      render: (code: string) => (
        <Text strong>{code || "Chưa có mã"}</Text>
      ),
    },
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (name: string, record) => (
        <div className="flex items-center gap-3">
          <img
            src={record.main_image}
            alt={name}
            className="w-10 h-10 object-cover rounded"
            onError={(e) => {
              (e.target as HTMLImageElement).src = "/placeholder-image.png";
            }}
          />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-gray-500">
              {record.category_name || "Chưa có danh mục"}
            </div>
          </div>
        </div>
      ),
    },
    // Đã ẩn c<PERSON>c cột: Đơn vị, Khối l<PERSON>ợng, Đơn gi<PERSON>, Số lượng, Thành tiền
  ];
}
