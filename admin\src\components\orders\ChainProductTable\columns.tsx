import { Button, Typography } from "antd";
import { PlusOutlined, CheckOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import { ChainProductItem } from "@/hooks/orders";

const { Text } = Typography;

interface CreateColumnsProps {
  handleProductSelect: (product: ChainProductItem) => void;
}

export function createChainProductColumns({
  handleProductSelect
}: CreateColumnsProps): ColumnsType<ChainProductItem> {
  return [
    {
      title: "Mã sản phẩm",
      dataIndex: "code",
      key: "code",
      width: 120,
      render: (code: string) => (
        <Text strong>{code || "Chưa có mã"}</Text>
      ),
    },
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (name: string, record) => (
        <div className="flex items-center gap-3">
          <img
            src={record.main_image}
            alt={name}
            className="w-10 h-10 object-cover rounded"
            onError={(e) => {
              (e.target as HTMLImageElement).src = "/placeholder-image.png";
            }}
          />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-gray-500">
              {record.category_name || "Chưa có danh mục"}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Chọn",
      key: "select",
      width: 120,
      align: "center" as const,
      render: (_, record) => (
        <Button
          type={record.quantity > 0 ? "primary" : "default"}
          icon={record.quantity > 0 ? <CheckOutlined /> : <PlusOutlined />}
          onClick={() => handleProductSelect(record)}
          size="small"
        >
          {record.quantity > 0 ? `Đã chọn (${record.quantity})` : "Chọn"}
        </Button>
      ),
    },
    // Đã ẩn các cột: Đơn vị, Khối lượng, Đơn giá, Thành tiền
  ];
}
