import { InputNumber, Typography } from "antd";
import type { ColumnsType } from "antd/es/table";
import { formatCurrency } from '@/lib/utils';
import { ChainProductItem } from "@/hooks/orders";

const { Text } = Typography;

interface CreateColumnsProps {
  handleQuantityChange: (productId: number, quantity: number) => void;
}

export function createChainProductColumns({ 
  handleQuantityChange 
}: CreateColumnsProps): ColumnsType<ChainProductItem> {
  return [
    {
      title: "Mã sản phẩm",
      dataIndex: "code",
      key: "code",
      width: 120,
      render: (code: string) => (
        <Text strong>{code || "Chưa có mã"}</Text>
      ),
    },
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (name: string, record) => (
        <div className="flex items-center gap-3">
          <img
            src={record.main_image}
            alt={name}
            className="w-10 h-10 object-cover rounded"
            onError={(e) => {
              (e.target as HTMLImageElement).src = "/placeholder-image.png";
            }}
          />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-gray-500">
              {record.category_name || "Chưa có danh mục"}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Đơn vị",
      dataIndex: "unit",
      key: "unit",
      width: 80,
      render: (unit: string) => unit || "Chưa có",
    },
    {
      title: "Khối lượng",
      dataIndex: "weight",
      key: "weight",
      width: 100,
      render: (weight: number) => weight ? `${weight} kg` : "Chưa có",
    },
    {
      title: "Đơn giá",
      dataIndex: "chain_price",
      key: "chain_price",
      width: 120,
      render: (chain_price: number) => (
        <Text strong className="text-blue-600">
          {formatCurrency(chain_price)}
        </Text>
      ),
    },
    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      width: 120,
      render: (quantity: number, record) => (
        <InputNumber
          min={0}
          value={quantity}
          onChange={(value) => handleQuantityChange(record.id, value || 0)}
          className="w-full"
          placeholder="0"
        />
      ),
    },
    {
      title: "Thành tiền",
      dataIndex: "total_price",
      key: "total_price",
      width: 150,
      render: (total_price: number) => (
        <Text strong className="text-green-600">
          {formatCurrency(total_price)}
        </Text>
      ),
    },
  ];
}
