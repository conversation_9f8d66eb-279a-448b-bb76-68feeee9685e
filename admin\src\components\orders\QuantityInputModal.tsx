import { useState, useEffect } from "react";
import { Modal, InputNumber, Typography, Space, Image } from "antd";
import { formatCurrency } from "@/lib/utils";
import { ChainProductItem } from "@/hooks/orders";

const { Text, Title } = Typography;

interface QuantityInputModalProps {
  isOpen: boolean;
  product: ChainProductItem | null;
  onConfirm: (quantity: number) => void;
  onCancel: () => void;
}

export default function QuantityInputModal({
  isOpen,
  product,
  onConfirm,
  onCancel,
}: QuantityInputModalProps) {
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (product) {
      setQuantity(product.quantity > 0 ? product.quantity : 1);
    }
  }, [product]);

  const handleConfirm = () => {
    onConfirm(quantity);
    setQuantity(1);
  };

  const handleCancel = () => {
    onCancel();
    setQuantity(1);
  };

  if (!product) return null;

  const totalPrice = quantity * (product.chain_price || 0);

  return (
    <Modal
      title="Chọn số lượng sản phẩm"
      open={isOpen}
      onOk={handleConfirm}
      onCancel={handleCancel}
      okText="Xác nhận"
      cancelText="Hủy"
      width={500}
    >
      <div className="space-y-4">
        {/* Product Info */}
        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
          <Image
            src={product.main_image}
            alt={product.name}
            width={80}
            height={80}
            className="object-cover rounded"
            fallback="/placeholder-image.png"
          />
          <div className="flex-1">
            <Title level={5} className="mb-1">
              {product.name}
            </Title>
            <Text type="secondary" className="block">
              Mã: {product.code || "Chưa có mã"}
            </Text>
            <Text type="secondary" className="block">
              Danh mục: {product.category_name || "Chưa có danh mục"}
            </Text>
            <Text strong className="text-blue-600">
              Đơn giá: {formatCurrency(product.chain_price || 0)}
            </Text>
          </div>
        </div>

        {/* Quantity Input */}
        <div className="space-y-2">
          <Text strong>Số lượng:</Text>
          <InputNumber
            min={0}
            value={quantity}
            onChange={(value) => setQuantity(value || 0)}
            className="w-full"
            size="large"
            placeholder="Nhập số lượng"
          />
        </div>

        {/* Total Price */}
        <div className="p-3 bg-green-50 rounded-lg">
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex justify-between">
              <Text>Số lượng:</Text>
              <Text strong>{quantity}</Text>
            </div>
            <div className="flex justify-between">
              <Text>Đơn giá:</Text>
              <Text>{formatCurrency(product.chain_price || 0)}</Text>
            </div>
            <div className="flex justify-between border-t pt-2">
              <Text strong>Thành tiền:</Text>
              <Text strong className="text-green-600 text-lg">
                {formatCurrency(totalPrice)}
              </Text>
            </div>
          </Space>
        </div>

        {quantity === 0 && (
          <div className="p-3 bg-yellow-50 rounded-lg">
            <Text type="warning">
              Nhập số lượng 0 sẽ bỏ chọn sản phẩm này khỏi danh sách.
            </Text>
          </div>
        )}
      </div>
    </Modal>
  );
}
