import { Card, Typography, Empty, But<PERSON>, Space, Tag } from "antd";
import { DeleteOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { formatCurrency } from '@/lib/utils';
import { type ChainProductItem } from "@/hooks/orders";

const { Title, Text } = Typography;

interface SelectedChainProductsProps {
  selectedProducts: ChainProductItem[];
  onRemoveProduct?: (productId: number) => void;
  onClearAll?: () => void;
  className?: string;
}

export default function SelectedChainProducts({
  selectedProducts,
  onRemoveProduct,
  onClearAll,
  className = ""
}: SelectedChainProductsProps) {
  const totalAmount = selectedProducts.reduce((sum, product) => sum + product.total_price, 0);
  const totalQuantity = selectedProducts.reduce((sum, product) => sum + product.quantity, 0);

  if (selectedProducts.length === 0) {
    return (
      <Card 
        title={
          <div className="flex items-center gap-2">
            <ShoppingCartOutlined className="text-blue-500" />
            <span>Sản phẩm đã chọn</span>
          </div>
        }
        className={`${className}`}
      >
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Chưa có sản phẩm nào được chọn"
        />
      </Card>
    );
  }

  return (
    <Card 
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShoppingCartOutlined className="text-blue-500" />
            <span>Sản phẩm đã chọn</span>
            <Tag color="blue" className="ml-2">
              {selectedProducts.length} sản phẩm
            </Tag>
          </div>
          {onClearAll && (
            <Button 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
              onClick={onClearAll}
            >
              Xóa tất cả
            </Button>
          )}
        </div>
      }
      className={`${className}`}
    >
      <div className="space-y-4">
        {/* Summary Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text type="secondary">Tổng số lượng:</Text>
              <div className="text-lg font-semibold text-blue-600">
                {totalQuantity.toLocaleString()}
              </div>
            </div>
            <div>
              <Text type="secondary">Tổng tiền:</Text>
              <div className="text-lg font-semibold text-green-600">
                {formatCurrency(totalAmount)}
              </div>
            </div>
          </div>
        </div>

        {/* Products List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {selectedProducts.map((product) => (
            <div 
              key={product.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-start gap-3">
                  {product.image && (
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-12 h-12 object-cover rounded border"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900 truncate">
                      {product.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      Mã: {product.code}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-4 ml-4">
                <div className="text-right">
                  <div className="text-sm text-gray-500">
                    {formatCurrency(product.price)} × {product.quantity}
                  </div>
                  <div className="font-semibold text-green-600">
                    {formatCurrency(product.total_price)}
                  </div>
                </div>
                
                {onRemoveProduct && (
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => onRemoveProduct(product.id)}
                    className="flex-shrink-0"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Summary */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <div>
              <Text type="secondary">
                Tổng cộng: {selectedProducts.length} sản phẩm, {totalQuantity.toLocaleString()} đơn vị
              </Text>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Tổng tiền</div>
              <div className="text-xl font-bold text-green-600">
                {formatCurrency(totalAmount)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
