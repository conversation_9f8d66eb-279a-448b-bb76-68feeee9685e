from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from ..models import Order
from ..serializers.order_serializers import OrderSerializer, OrderCreateSerializer, OrderUpdateSerializer
from ..services.email_service import EmailService
from ..services.message_service import send_order_notification
from ..permissions import IsAdminUserOrOrderOwner
from ..services.order_service import OrderService
from django.utils import timezone
import json
import datetime
from django.contrib.auth.models import User
from ..services.message_service import send_direct_order_notification
from ..utils.sales_admin_filter import build_sales_admin_filter

class OrderViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing orders.
    """
    permission_classes = [IsAuthenticated, IsAdminUserOrOrderOwner]
    serializer_class = OrderSerializer
    queryset = Order.objects.all().order_by('-created_at')
    pagination_class = PageNumberPagination

    def get_queryset(self):
        """
        Filter orders based on query parameters and user permissions.
        Supports:
        - Status filtering
        - Date range filtering (date_from, date_to) with date_type ('created' or 'completed')
        - Search by ID, email, or phone number
        - Pagination
        """
        if getattr(self, 'swagger_fake_view', False):  # Handle schema generation
            return Order.objects.none()

        queryset = Order.objects.prefetch_related('items__product').all().order_by('-created_at')

        # Filter by user unless staff
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)

        # Filter by status if provided (supports multiple values)
        status = self.request.query_params.get('status')
        if status:
            # Handle comma-separated values
            if ',' in status:
                statuses = [s.strip() for s in status.split(',') if s.strip()]
                queryset = queryset.filter(status__in=statuses)
            else:
                queryset = queryset.filter(status=status)

        # Filter by sales_admin if provided (supports multiple values and null)
        sales_admin = self.request.query_params.get('sales_admin')

        sales_admin_filter = build_sales_admin_filter(sales_admin)

        if sales_admin_filter:
            queryset = queryset.filter(sales_admin_filter)
                
        # Filter by delivery_staff if provided (supports multiple values)
        delivery_staff = self.request.query_params.get('delivery_staff')
        if delivery_staff:
            # Handle comma-separated values
            if ',' in delivery_staff:
                delivery_staff_ids = [id.strip() for id in delivery_staff.split(',') if id.strip()]
                queryset = queryset.filter(delivery_staff_id__in=delivery_staff_ids)
            else:
                queryset = queryset.filter(delivery_staff_id=delivery_staff)

        # Filter by shipping_unit if provided (supports multiple values)
        shipping_unit = self.request.query_params.get('shipping_unit')
        if shipping_unit:
            # Handle comma-separated values
            if ',' in shipping_unit:
                shipping_units = [unit.strip() for unit in shipping_unit.split(',') if unit.strip()]
                queryset = queryset.filter(shipping_unit__in=shipping_units)
            else:
                queryset = queryset.filter(shipping_unit=shipping_unit)

        # Filter by payment_status if provided
        payment_status_str = self.request.query_params.get('payment_status')
        if payment_status_str:
            payment_statuses = [status.strip() for status in payment_status_str.split(',') if status.strip()]
            if payment_statuses:
                queryset = queryset.filter(payment_status__in=payment_statuses)

        # Filter by order_type if provided (e.g., showroom, e_comm, chain, normal)
        order_type = self.request.query_params.get('order_type')
        if order_type:
            order_types = [t.strip().lower() for t in order_type.split(',') if t.strip()]
            
            q_filter = Q()
            
            if 'showroom' in order_types:
                q_filter |= Q(is_showroom=True)
            
            if 'e_comm' in order_types:
                q_filter |= Q(is_e_comm=True)

            if 'chain' in order_types:
                q_filter |= Q(is_chain=True)

            # 'normal' refers to orders that are neither showroom, e-commerce, nor chain
            if 'normal' in order_types:
                q_filter |= Q(is_showroom=False, is_e_comm=False, is_chain=False)

            # If any type was specified, apply the filter
            if q_filter:
                queryset = queryset.filter(q_filter)

        # Handle date range filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        date_type = self.request.query_params.get('date_type', 'created')  # 'created' or 'completed'

        # Determine which date field to filter by
        if date_type == 'completed':
            date_field_gte = 'completion_time__gte'
            date_field_lt = 'completion_time__lt'
            date_field_lte = 'completion_time__lte'
        else:  # default to 'created'
            date_field_gte = 'created_at__gte'
            date_field_lt = 'created_at__lt'
            date_field_lte = 'created_at__lte'

        if date_from:
            filter_kwargs = {date_field_gte: date_from}
            queryset = queryset.filter(**filter_kwargs)
        if date_to:
            # Add 1 day to include all orders on the end date
            from datetime import datetime, timedelta
            try:
                end_date = datetime.strptime(date_to, '%Y-%m-%d')
                end_date = end_date + timedelta(days=1)
                filter_kwargs = {date_field_lt: end_date}
                queryset = queryset.filter(**filter_kwargs)
            except ValueError:
                # If date parsing fails, use the original date string
                filter_kwargs = {date_field_lte: date_to}
                queryset = queryset.filter(**filter_kwargs)

        # Handle search with search_by parameter
        search = self.request.query_params.get('search', self.request.query_params.get('query'))  # Support both search and query params
        search_by = self.request.query_params.get('search_by', self.request.query_params.get('searchBy', 'id'))  # Support both search_by and searchBy

        if search:
            if search_by == 'id':
                # Handle comma-separated order IDs
                if ',' in search:
                    order_ids = [id.strip() for id in search.split(',') if id.strip()]
                    queryset = queryset.filter(id__in=order_ids)
                else:
                    queryset = queryset.filter(id__icontains=search)
            elif search_by == 'email':
                queryset = queryset.filter(email__icontains=search)
            elif search_by == 'phone':
                queryset = queryset.filter(phone_number__icontains=search)
            elif search_by == 'name':
                queryset = queryset.filter(
                    Q(user__first_name__icontains=search) |
                    Q(user__last_name__icontains=search)
                )
            else:
                # Default to searching across multiple fields
                queryset = queryset.filter(
                    Q(id__icontains=search) |
                    Q(email__icontains=search) |
                    Q(phone_number__icontains=search) |
                    Q(user__first_name__icontains=search) |
                    Q(user__last_name__icontains=search)
                )

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        if request.query_params.get('no_page', '').lower() == 'true':
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'count': queryset.count(),
                'next': None,
                'previous': None,
                'results': serializer.data
            })

        # Use default pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def get_serializer_class(self):
        """
        Use different serializers for different operations:
        - OrderCreateSerializer for creation
        - OrderUpdateSerializer for updates
        - OrderSerializer for listing and retrieval
        """
        if self.action == 'create':
            return OrderCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return OrderUpdateSerializer
        return OrderSerializer

    @action(detail=False, methods=['get'])
    def by_sales_admin(self, request):
        """
        Get orders assigned to specific sales admin(s).
        URL: /api/orders/by_sales_admin/?id={sales_admin_id} or ?sales_admin={sales_admin_id}
        Supports multiple IDs: ?sales_admin=1,2,3
        """
        sales_admin_id = request.query_params.get('id')
        sales_admin_param = request.query_params.get('sales_admin')

        # Use sales_admin parameter if provided, otherwise fall back to id parameter
        sales_admin_value = sales_admin_param or sales_admin_id

        if not sales_admin_value:
            return Response(
                {'error': 'sales_admin_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle comma-separated values
        if ',' in sales_admin_value:
            sales_admin_ids = [id.strip() for id in sales_admin_value.split(',') if id.strip()]
            queryset = self.get_queryset().filter(sales_admin_id__in=sales_admin_ids)
        else:
            queryset = self.get_queryset().filter(sales_admin_id=sales_admin_value)

        if request.query_params.get('no_page', '').lower() == 'true':
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'count': queryset.count(),
                'next': None,
                'previous': None,
                'results': serializer.data
            })

        # Use default pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_orders(self, request):
        """
        Get orders for the currently authenticated user only.
        This endpoint ensures users can only see their own orders.
        Supports pagination and status filtering.
        """
        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Always filter by current user - no exceptions
        queryset = Order.objects.filter(user=request.user).order_by('-created_at')

        # Apply status filter if provided
        status_filter = request.query_params.get('status')
        if status_filter:
            if ',' in status_filter:
                statuses = [s.strip() for s in status_filter.split(',') if s.strip()]
                queryset = queryset.filter(status__in=statuses)
            else:
                queryset = queryset.filter(status=status_filter)

        # Handle pagination
        if request.query_params.get('no_page', '').lower() == 'true':
            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'count': queryset.count(),
                'next': None,
                'previous': None,
                'results': serializer.data
            })

        # Use default pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def status_counts(self, request):
        """
        Get counts of orders by status.
        """
        queryset = self.get_queryset()
        counts = {
            status: queryset.filter(status=status).count()
            for status, _ in Order.STATUS_CHOICES
        }
        return Response(counts)

    @action(detail=False, methods=['post'])
    def reset_timers(self, request):
        """
        Reset confirmation_time for all processing orders when outside working hours.
        This endpoint should be called at 3PM to reset all timers.
        """
        try:
            now = timezone.now()
            local_time = timezone.localtime(now)

            print(f"[DEBUG]: timenow {now} - localtime {local_time}")
            # Lấy tất cả đơn hàng đang xử lý
            processing_orders = Order.objects.filter(status='processing')

            next_working_day = local_time
            current_hour = local_time.hour

            if current_hour >= 15:
                next_working_day = local_time + timezone.timedelta(days=1)

            next_working_day = next_working_day.replace(hour=8, minute=0, second=0, microsecond=0)

            next_working_day_utc = next_working_day.astimezone(datetime.timezone.utc)

            count = processing_orders.update(confirmation_time=next_working_day_utc)

            return Response({
                'message': f'Reset {count} order timers to 8AM of next working day',
                'next_working_day': next_working_day.isoformat(),
                'next_working_day_utc': next_working_day_utc.isoformat(),
                'current_time': local_time.isoformat(),
                'current_hour': current_hour
            })
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create(self, request, *args, **kwargs):
        """
        Create a new order.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Note: user is handled inside the serializer's create method
        order = serializer.save()

        # Send order confirmation email and Telegram notification
        try:
            EmailService.send_order_notification(order)
            # Refresh the order instance to get all related data
            order = Order.objects.get(id=order.id)

            # Send order notification with the actual order status
            # Skip Discord notification for chain orders (will be implemented later)
            send_discord = not order.is_chain
            send_order_notification(order, order.status, send_to_discord=send_discord, is_update=False)
        except Exception as e:
            # Log the error but don't fail the request
            print(f"Error sending notifications: {e}")

        return Response(
            OrderSerializer(order).data,
            status=status.HTTP_201_CREATED
        )

    def update(self, request, *args, **kwargs):
        """
        Update an order with optional selective notifications.
        If updatedList is provided, sends selective update notification.
        Also handles removal of delivery staff when delivery_staff is set to null.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Get the updatedList from request data
        updated_list = request.data.get('updatedList', [])

        # Handle delivery staff update/removal if specified in request
        if 'delivery_staff_id' in request.data:
            delivery_staff_id = request.data['delivery_staff_id']
            if delivery_staff_id:
                try:
                    from django.contrib.auth.models import User
                    delivery_staff = User.objects.get(id=delivery_staff_id)
                    instance.delivery_staff = delivery_staff
                    instance.shipping_unit = "motorbike"
                except User.DoesNotExist:
                    return Response(
                        {'error': 'Invalid delivery staff ID'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                instance.delivery_staff = None
                instance.shipping_unit = None
            instance.save()

        # Store previous status before update for rank calculation
        previous_status = instance.status

        

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # Check if status was updated and handle rank updates
        updated_instance = self.get_object()  # Get fresh instance after update
        if 'status' in request.data and updated_instance.status != previous_status:
            OrderService.handle_order_status_change(updated_instance, updated_instance.status, previous_status)

        # Send selective update notification if updatedList is provided
        if updated_list:
            try:
                from ..services.message_service import send_selective_order_update_notification
                # Skip Discord notification for chain orders (will be implemented later)
                send_discord = not updated_instance.is_chain
                send_selective_order_update_notification(
                    updated_instance,
                    updated_list,
                    nextStatus=None,  # No next status for regular updates
                    send_to_discord=send_discord
                )
                print(f"Sent selective update notification for order #{updated_instance.id} with fields: {updated_list}")
            except Exception as e:
                print(f"Error sending selective update notification: {e}")

        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """
        Update the status and optionally delivery staff of an order.
        """
        order = self.get_object()
        previous_status = order.status  # Store the previous status before updating
        new_status = request.data.get('status')
        delivery_staff_id = request.data.get('delivery_staff_id')
        shipping_unit = request.data.get('shipping_unit')

        if not new_status:
            return Response(
                {'error': 'Status is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if new_status not in ['pending', 'accounting_processing', 'processing', 'shipped', 'delivered', 'cancelled', 'returned']:
            return Response(
                {'error': 'Invalid status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle delivery staff update/removal
        if delivery_staff_id is not None:  # Check if the field was included in request
            if delivery_staff_id:  # If value is provided, assign new delivery staff
                try:

                    delivery_staff = User.objects.get(id=delivery_staff_id)
                    order.delivery_staff = delivery_staff
                    order.shipping_unit = "motorbike"
                    # Send direct notification to delivery staff
                except User.DoesNotExist:
                    return Response(
                        {'error': 'Invalid delivery staff ID'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:  # If value is empty/null/false, remove delivery staff
                order.delivery_staff = None
                order.shipping_unit = None

        if shipping_unit:
            order.shipping_unit = shipping_unit

        order.status = new_status        # Cập nhật thời gian xác nhận khi chuyển sang trạng thái "processing"
        if new_status in ['processing', 'accounting_processing'] and previous_status == 'pending':
            now = timezone.now()
            local_time = timezone.localtime(now)
            print(f"[DEBUG]: timenow {now} - localtime {local_time}")
            current_hour = local_time.hour
            if current_hour >= 15 or current_hour < 8:
                next_working_day = local_time
                if current_hour >= 15:
                    next_working_day = local_time + timezone.timedelta(days=1)
                else:
                    pass

                next_working_day = next_working_day.replace(hour=8, minute=0, second=0, microsecond=0)
                order.confirmation_time = next_working_day.astimezone(datetime.timezone.utc)
            else:
                order.confirmation_time = now

        # Cập nhật thời gian hoàn thành khi chuyển sang trạng thái "delivered"
        if new_status == 'delivered':
            order.completion_time = timezone.now()
            order.payment_received = True

        order.save()

        # Handle rank updates when order status changes

        OrderService.handle_order_status_change(order, new_status, previous_status)

        if delivery_staff_id:

            send_direct_order_notification(order, delivery_staff_id)

        # Define the order status sequence
        status_sequence = ['pending', 'accounting_processing', 'processing', 'shipped', 'delivered']

        # Check if this is a forward status transition or to cancelled status
        is_forward_transition = (
            (status_sequence.index(new_status) > status_sequence.index(previous_status)
             if previous_status in status_sequence and new_status in status_sequence
             else False) or new_status == 'cancelled'
        )

        # Only send notifications for forward transitions or cancellations
        if is_forward_transition:
            try:
                notification_status_map = {
                    'pending': 'accounting_processing',
                    'accounting_processing': 'processing',
                    'processing': 'shipped',
                    'shipped': 'delivered',
                    'delivered': 'done',
                    'cancelled': 'done',
                    'returned': 'done'
                }
                next_status = notification_status_map.get(new_status)
                # Skip Discord notification for chain orders (will be implemented later)
                send_discord = not order.is_chain
                send_order_notification(order, next_status, send_to_discord=send_discord, is_update=True)
            except Exception as e:
                print(f"Error sending update notification: {e}")

            # Send status update email
            try:
                EmailService.send_order_notification(order, status=new_status)
            except Exception as e:
                # Log the error but don't fail the request
                print(f"Error sending status update notification: {e}")
        else:
            print(f"Skipping notification for backward transition from {previous_status} to {new_status}")

        return Response(OrderSerializer(order).data)
