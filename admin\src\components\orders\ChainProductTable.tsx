import { Table, Typography, Spin, Input, Button, Pagination, Row, Col } from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import { useChainProductTable, type ChainProductItem } from "@/hooks/orders";
import { createChainProductColumns } from "./ChainProductTable/columns";
import SelectedChainProducts from "./SelectedChainProducts";
import QuantityInputModal from "./QuantityInputModal";
import { useState } from "react";

const { Text } = Typography;

interface ChainProductTableProps {
  onProductsChange: (products: ChainProductItem[]) => void;
  showSelectedProducts?: boolean;
}

const PAGE_SIZE = 10;

export default function ChainProductTable({ onProductsChange, showSelectedProducts = true }: ChainProductTableProps) {
  const [selectedProducts, setSelectedProducts] = useState<ChainProductItem[]>([]);
  const [isQuantityModalOpen, setIsQuantityModalOpen] = useState(false);
  const [selectedProductForQuantity, setSelectedProductForQuantity] = useState<ChainProductItem | null>(null);

  // Update selected products when products change
  const handleProductsChange = (products: ChainProductItem[]) => {
    setSelectedProducts(products);
    onProductsChange(products);
  };

  const {
    // Data
    products,
    data,

    // Loading states
    isLoading,
    isFetching,

    // Search & Pagination
    searchQuery,
    currentPage,

    // Calculated values
    totalAmount,
    selectedCount,
    totalSelectedAcrossAllPages,
    startIndex,
    endIndex,

    // Handlers
    handleQuantityChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleClearAll,
  } = useChainProductTable({
    onProductsChange: handleProductsChange,
    pageSize: PAGE_SIZE
  });

  // Handler for row click (product selection)
  const handleRowClick = (product: ChainProductItem) => {
    setSelectedProductForQuantity(product);
    setIsQuantityModalOpen(true);
  };

  // Handler for quantity confirmation
  const handleQuantityConfirm = (quantity: number) => {
    if (selectedProductForQuantity) {
      handleQuantityChange(selectedProductForQuantity.id, quantity);
    }
    setIsQuantityModalOpen(false);
    setSelectedProductForQuantity(null);
  };

  // Handler for quantity modal cancel
  const handleQuantityCancel = () => {
    setIsQuantityModalOpen(false);
    setSelectedProductForQuantity(null);
  };

  const columns = createChainProductColumns();

  // Handler for removing a product from selected list
  const handleRemoveProduct = (productId: number) => {
    // Use the hook's quantity change handler to set quantity to 0
    handleQuantityChange(productId, 0);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search Section */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex-1 min-w-[300px]">
          <Input
            placeholder="Tìm kiếm sản phẩm theo tên, mã sản phẩm, danh mục..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full"
            size="large"
            allowClear
            prefix={<SearchOutlined />}
          />
        </div>
        <Button
          size="large"
          icon={<ReloadOutlined />}
          onClick={handleReset}
          disabled={!searchQuery}
        >
          Đặt lại
        </Button>
      </div>

      <Row gutter={[24, 24]}>
        {/* Left Column - Product Table */}
        <Col xs={24} lg={showSelectedProducts ? 16 : 24}>
          {/* Header Section */}
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-lg font-semibold">Danh sách sản phẩm chuỗi</h3>
              <Text type="secondary">
                {data?.count ? (
                  <>
                    Hiển thị {startIndex}-{endIndex} của {data.count} sản phẩm
                    {searchQuery && ` (tìm kiếm: "${searchQuery}")`} • Đã chọn {totalSelectedAcrossAllPages} sản phẩm (trang này: {selectedCount})
                  </>
                ) : (
                  `Đã chọn ${totalSelectedAcrossAllPages} sản phẩm`
                )}
              </Text>
            </div>
          </div>

          <Table
            columns={columns}
            dataSource={products}
            rowKey="id"
            pagination={false}
            scroll={{ x: 1000 }}
            size="middle"
            className="border rounded-lg"
            loading={isFetching}
            rowClassName={(record) =>
              record.quantity > 0 ? "bg-blue-50 cursor-pointer" : "cursor-pointer hover:bg-gray-50"
            }
            onRow={(record) => ({
              onClick: () => handleRowClick(record),
            })}
          />

          {/* Pagination */}
          {data && data.count > PAGE_SIZE && (
            <div className="flex justify-center mt-6">
              <Pagination
                current={currentPage}
                total={data.count}
                pageSize={PAGE_SIZE}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} sản phẩm`
                }
              />
            </div>
          )}

          {products.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ?
                `Không tìm thấy sản phẩm nào phù hợp với từ khóa "${searchQuery}"` :
                "Không có sản phẩm chuỗi nào được tìm thấy"
              }
            </div>
          )}
        </Col>

        {/* Right Column - Selected Products */}
        {showSelectedProducts && (
          <Col xs={24} lg={8}>
            <SelectedChainProducts
              selectedProducts={selectedProducts}
              onRemoveProduct={handleRemoveProduct}
              onClearAll={handleClearAll}
            />
          </Col>
        )}
      </Row>

      {/* Quantity Input Modal */}
      <QuantityInputModal
        isOpen={isQuantityModalOpen}
        product={selectedProductForQuantity}
        onConfirm={handleQuantityConfirm}
        onCancel={handleQuantityCancel}
      />
    </div>
  );
}
