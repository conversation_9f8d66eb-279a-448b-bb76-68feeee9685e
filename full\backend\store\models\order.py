from django.contrib.auth.models import User
from django.db import models
from safedelete.models import SafeDeleteModel
from safedelete.models import SOFT_DELETE, SOFT_DELETE_CASCADE
from .product import Product, ProductVariant

class Order(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE
    SHIPPING_UNIT_CHOICES = (
        ('company_vehicle', 'Xe Cty'),
        ('motorbike', 'Xe máy'),
        ('grab', 'Grab'),
        ('transport_partner', 'Chành xe'),
        ('shipping_partner', 'ĐVVC'),
    )

    PAYMENT_METHOD_CHOICES = (
        ('cod', 'COD'),
        ('cash', 'Tiền mặt'),
        ('bank_transfer', '<PERSON>yển <PERSON>'),
        ('sepay', 'Sepay'),
    )

    PAYMENT_STATUS_CHOICES = (
        ('paid', 'Đã thanh toán'),
        ('unpaid', 'Chưa thanh toán'),
    )

    STATUS_CHOICES = (
        ('pending', '<PERSON><PERSON> xác nhận'),
        ('accounting_processing', '<PERSON><PERSON> xử lý (kế toán)'),
        ('processing', '<PERSON>ang xử lý'),
        ('shipped', 'Đang giao hàng'),
        ('delivered', 'Đã giao hàng'),
        ('cancelled', 'Hủy bỏ'),
        ('returned', 'Đã trả hàng'),
        ('refunded', 'Đã hoàn tiền'),
    )

    SHOWROOM_STATUS_CHOICES = (
        ('transfer_to_warehouse', 'Điều chuyển hàng từ SR về Kho'),
        ('transfer_to_showroom', 'Điều chuyển hàng từ Kho về SR'),
        ('cancel_damaged', 'Huỷ hàng hư hỏng của SR'),
        ('showroom_order', 'Đặt hàng của SR'),
    )

    # User Relations
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')    # Order Information
    status = models.CharField(max_length=25, choices=STATUS_CHOICES, default='pending')
    total_price = models.DecimalField(max_digits=15, decimal_places=2)
    shipping_address = models.TextField()
    ward = models.CharField(max_length=100, blank=True, null=True)  # Phường
    district = models.CharField(max_length=100, blank=True, null=True)  # Quận
    city = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=20)
    email = models.EmailField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmation_time = models.DateTimeField(null=True, blank=True)  # Thời gian xác nhận đơn hàng
    completion_time = models.DateTimeField(null=True, blank=True)  # Thời gian hoàn thành đơn hàng
    sales_admin = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sales_orders')
    delivery_staff = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='delivery_orders')

    # Delivery Information
    delivery_date = models.DateField(null=True, blank=True)
    delivery_time = models.DateTimeField(null=True, blank=True)
    shipping_unit = models.CharField(max_length=20, choices=SHIPPING_UNIT_CHOICES, null=True, blank=True)
    shipping_fee = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, default=0)

    # Payment Information
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, null=True, blank=True)
    payment_status = models.CharField(max_length=10, choices=PAYMENT_STATUS_CHOICES, default='unpaid')
    company_payment_received = models.BooleanField(default=False)

    # Price Calculations
    discount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # Stored as decimal, e.g., 0.1 for 10%

    # Additional Fields
    notes = models.TextField(blank=True)
    is_showroom = models.BooleanField(default=False, db_index=True)
    showroom_status = models.CharField(max_length=30, choices=SHOWROOM_STATUS_CHOICES, null=True, blank=True)
    have_tax = models.BooleanField(default=True, null=False)
    is_printed = models.BooleanField(default=False, help_text="Indicates whether the order has been printed")
    is_e_comm = models.BooleanField(default=False, db_index=True, help_text="Indicates whether this is an e-commerce order")
    is_chain = models.BooleanField(default=False, db_index=True, help_text="Indicates whether this is a chain order")

    # Sepay Payment Fields
    sepay_transaction_id = models.CharField(max_length=100, null=True, blank=True, help_text="Sepay transaction ID")
    sepay_reference_code = models.CharField(max_length=100, null=True, blank=True, help_text="Sepay reference code from SMS")
    sepay_payment_content = models.TextField(null=True, blank=True, help_text="Sepay payment content/description")
    sepay_gateway = models.CharField(max_length=50, null=True, blank=True, help_text="Bank gateway name from Sepay")
    sepay_transfer_amount = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, help_text="Actual transfer amount from Sepay")

    def save(self, *args, **kwargs):
        """
        Override save method to automatically set tax to 0 when have_tax is False
        """
        if not self.have_tax:
            self.tax = 0
        super().save(*args, **kwargs)

    def get_subtotal_with_product_discounts(self):
        """
        Calculate subtotal with item-level discounts (chiet_khau_amount) applied.
        This replaces the old total_price calculation.
        """
        total = 0
        for item in self.items.all():
            total += item.get_total_price_with_discount()
        return total

    def get_final_price(self):
        """
        Calculate final price with new discount logic:
        1. First apply item-level discounts (chiet_khau_amount)
        2. Then apply order-level discounts (voucher + member)
        3. Apply tax and shipping fee
        Formula: ((subtotal_with_product_discounts - order_discount) * (1 + tax)) + shipping_fee
        """
        # Get subtotal with product discounts applied
        subtotal_with_product_discounts = self.get_subtotal_with_product_discounts()

        # Apply order-level discounts (voucher + member discounts)
        order_discount = self.discount or 0

        # Calculate amount after order discount (ensure it doesn't go negative)
        discounted_amount = max(0, subtotal_with_product_discounts - order_discount)

        # Apply tax to discounted amount
        if self.have_tax:
            taxed_amount = discounted_amount * (1 + self.tax)
        else:
            taxed_amount = discounted_amount

        # Add shipping fee
        shipping_fee = self.shipping_fee or 0
        final_price = taxed_amount + shipping_fee
        return final_price

    def get_revenue_price(self):
        """
        Calculate revenue price with new discount logic:
        (subtotal_with_product_discounts - order_discount) * (1 + tax) (excluding shipping fee)
        This is used for revenue reporting - represents actual product sales revenue.
        """
        # Get subtotal with product discounts applied
        subtotal_with_product_discounts = self.get_subtotal_with_product_discounts()

        # Apply order-level discounts (voucher + member discounts)
        order_discount = self.discount or 0

        # Calculate amount after order discount (ensure it doesn't go negative)
        discounted_amount = max(0, subtotal_with_product_discounts - order_discount)

        # Apply tax to discounted amount
        if self.have_tax:
            revenue_price = discounted_amount * (1 + self.tax)
        else:
            revenue_price = discounted_amount

        return revenue_price

    def __str__(self):
        return f"Order {self.id} - {self.user.username}"

class OrderItem(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="items")
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variant = models.ForeignKey(ProductVariant, on_delete=models.SET_NULL, null=True, blank=True)
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=15, decimal_places=2)
    total_price = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True, help_text="Exact total price for this item, overrides price * quantity calculation")
    chiet_khau_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, help_text="Số tiền chiết khấu áp dụng cho item này trong order")


    def __str__(self):
        variant_str = f" ({self.variant.name})" if self.variant else ""
        return f"{self.quantity}x {self.product.name}{variant_str} in Order {self.order.id}"

    def get_total_price(self):
        # Use exact total_price if available, otherwise calculate from price * quantity
        if self.total_price is not None:
            return self.total_price
        return self.price * self.quantity

    def get_total_price_with_discount(self):
        """
        Calculate total price with item-level discount (chiet_khau_amount) applied.
        Formula: Tổng sản phẩm = sum(quantity * price)
        If chiet_khau_amount > 0: discount_amount = Tổng sản phẩm - chiet_khau_amount
        """
        base_total = self.get_total_price()

        # Apply item-level discount if available
        if self.chiet_khau_amount and self.chiet_khau_amount > 0:
            # Ensure discount doesn't make the price negative
            discounted_total = max(0, base_total - self.chiet_khau_amount)
            return discounted_total

        return base_total

    def save(self, *args, **kwargs):
        # Set the price based on variant or product if not already set
        # Use 'is None' instead of 'not self.price' to allow price = 0
        if self.price is None:
            # Chain order logic
            if self.order.is_chain:
                if self.variant and self.variant.price is not None:
                    # Variant price takes precedence
                    self.price = self.variant.price
                else:
                    # Fallback to product's chain price
                    self.price = self.product.chain_price
            # Normal order logic
            else:
                if self.variant:
                    if self.variant.discount_price is not None:
                        self.price = self.variant.discount_price
                    elif self.variant.price is not None:
                        self.price = self.variant.price
                    elif self.product.discount_price is not None:
                        self.price = self.product.discount_price
                    else:
                        self.price = self.product.price
                else:
                    self.price = self.product.discount_price or self.product.price
        
        # Ensure chiet_khau_amount is not None
        if self.chiet_khau_amount is None:
            self.chiet_khau_amount = 0
            
        super().save(*args, **kwargs)

class OrderPromotion(SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE
    """
    Model for linking promotions to orders and tracking applied discounts
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='promotions')
    promotion = models.ForeignKey('store.Promotion', on_delete=models.CASCADE)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2)
    applied_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.promotion.code} applied to Order {self.order.id}"

    class Meta:
        unique_together = ('order', 'promotion')
